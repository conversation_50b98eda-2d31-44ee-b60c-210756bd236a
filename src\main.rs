use log::{info, error};
use anyhow::Result;
use env_logger::{Builder, Env, fmt::Color};
use std::io::Write;

use pump_parser::core::processing::ultimate_pipeline::UltimatePipeline;
use pump_parser::types::{init_config, get_config};
use pump_parser::get_parser_info;
use pump_parser::core::stats::plugin_stats::start_plugin_stats_reporting;

// 将字符串日志级别转换为LevelFilter
fn get_log_level(level: &str) -> log::LevelFilter {
    match level.to_lowercase().as_str() {
        "error" => log::LevelFilter::Error,
        "warn" => log::LevelFilter::Warn,
        "info" => log::LevelFilter::Info,
        "debug" => log::LevelFilter::Debug,
        "trace" => log::LevelFilter::Trace,
        _ => log::LevelFilter::Info, // 默认为Info级别
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化配置（先读取配置，再初始化日志）
    init_config()?;
    let config = get_config();
    
    // 获取配置文件中的日志级别
    let log_level = get_log_level(&config.logging.level);
    
    // 初始化日志 - 自定义格式，去掉模块名称前缀，添加颜色支持
    Builder::new()
        .format(|buf, record| {
            let mut level_style = buf.style();
            
            // 根据日志级别设置不同颜色
            match record.level() {
                log::Level::Error => level_style.set_color(Color::Red).set_bold(true),
                log::Level::Warn => level_style.set_color(Color::Yellow).set_bold(true),
                log::Level::Info => level_style.set_color(Color::Green),
                log::Level::Debug => level_style.set_color(Color::Blue),
                log::Level::Trace => level_style.set_color(Color::Magenta),
            };
            
            writeln!(
                buf,
                "[{}] {} {}",
                chrono::Utc::now().format("%Y-%m-%dT%H:%M:%SZ"),
                level_style.value(record.level()),
                record.args()
            )
        })
        .filter_level(log_level) // 使用配置文件中的日志级别
        .parse_env(env_logger::Env::default()) // 允许通过环境变量覆盖
        .init();
    
    info!("[启动] 多协议交易解析监控系统 v1.0.0");
    
    // 创建流水线配置
    let pipeline_config = config.to_pipeline_config();
    let parser_threads = if pipeline_config.parser_threads == 0 { num_cpus::get() * 2 } else { pipeline_config.parser_threads };
    let publisher_threads = if pipeline_config.publisher_threads == 0 { num_cpus::get() } else { pipeline_config.publisher_threads };
    
    info!("[配置] 解析器: {} 线程, 发布器: {} 线程, 队列: {} 容量", 
        parser_threads, publisher_threads, pipeline_config.queue_capacity);
    info!("[网络] gRPC: {}", config.grpc.endpoint);
    info!("[存储] Redis: {}", config.redis.url);
    
    // 启动插件统计报告
    start_plugin_stats_reporting();
    
    // 创建并启动终极流水线
    let mut pipeline = UltimatePipeline::new(pipeline_config);
    
    info!("[系统] 启动流水线处理器");
    info!("[系统] 按 Ctrl+C 停止监听");
    
    // 启动流水线
    tokio::select! {
        result = pipeline.start() => {
            if let Err(e) = result {
                error!("[错误] 流水线启动失败: {}", e);
            }
        }
        _ = tokio::signal::ctrl_c() => {
            info!("[关闭] 接收到中断信号，正在关闭...");
            pipeline.set_running(false);
        }
    }
    
    // 优雅关闭
    pipeline.shutdown().await?;
    
    info!("[关闭] 程序已安全退出");
    Ok(())
}

