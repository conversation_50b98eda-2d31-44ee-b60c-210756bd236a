use serde::{Deserialize, Serialize};
use std::{env, fs, path::Path};
use thiserror::Error;
use std::sync::Once;
use std::sync::OnceLock;

#[derive(Error, Debug)]
pub enum ConfigError {
    #[error("环境变量错误: {0}")]
    EnvVarError(String),
    
    #[error("配置验证错误: {0}")]
    ValidationError(String),
    
    #[error("文件读取错误: {0}")]
    FileError(String),
    
    #[error("配置解析错误: {0}")]
    ParseError(String),
    
    #[error("其他错误: {0}")]
    Other(String),
}

/// gRPC配置
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct GrpcConfig {
    /// gRPC端点
    pub endpoint: String,
    /// 连接超时时间(秒)
    pub timeout: u64,
    /// 重连间隔(秒)
    pub reconnect_interval: u64,
}

/// Redis配置
#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]
pub struct RedisConfig {
    /// Redis连接URL
    pub url: String,
    /// 发布通道（保留向后兼容）
    pub channel: String,
    /// Bonk交易发布频道
    pub bonk_channel: String,
    /// Pump交易发布频道
    pub pump_channel: String,
    /// 连接池大小
    pub pool_size: usize,
}

/// 处理器配置
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ProcessorConfig {
    /// 解析线程数
    pub parser_threads: usize,
    /// 发布线程数
    pub publisher_threads: usize,
    /// gRPC缓冲区大小
    pub grpc_buffer_size: usize,
    /// 队列容量
    pub queue_capacity: usize,
    /// 批处理大小
    pub batch_size: usize,
}

/// 程序配置
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ProgramsConfig {
    /// PumpFun程序ID
    pub pump_program: String,
    /// Bonk程序ID
    pub bonk_program: String,
}

/// 日志配置
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 是否输出到文件
    pub file_output: bool,
    /// 日志文件路径
    pub file_path: String,
}

/// 应用配置
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AppConfig {
    /// gRPC配置
    pub grpc: GrpcConfig,
    /// Redis配置
    pub redis: RedisConfig,
    /// 处理器配置
    pub processor: ProcessorConfig,
    /// 程序配置
    pub programs: ProgramsConfig,
    /// 日志配置
    pub logging: LoggingConfig,
}

impl Default for AppConfig {
    fn default() -> Self {
        let cores = num_cpus::get();
        
        Self {
            grpc: GrpcConfig {
                endpoint: "solana-yellowstone-grpc.publicnode.com:443".to_string(),
                timeout: 30,
                reconnect_interval: 5,
            },
            redis: RedisConfig {
                url: "redis://127.0.0.1/".to_string(),
                channel: "trades_channel".to_string(),
                bonk_channel: "bonk_channel".to_string(),
                pump_channel: "pump_channel".to_string(),
                pool_size: 50,
            },
            processor: ProcessorConfig {
                parser_threads: cores * 2,
                publisher_threads: cores,
                grpc_buffer_size: 100000,
                queue_capacity: 1000000,
                batch_size: 1000,
            },
            programs: ProgramsConfig {
                pump_program: "54HsPQAHuYZJU8LkJ2Li4AdyVE5hxpNK5SmRnSgg1uns".to_string(),
                bonk_program: "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj".to_string(),
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file_output: false,
                file_path: "logs/parser.log".to_string(),
            },
        }
    }
}

impl AppConfig {
    /// 从配置文件加载配置
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self, ConfigError> {
        let content = fs::read_to_string(path)
            .map_err(|e| ConfigError::FileError(format!("读取配置文件失败: {}", e)))?;
            
        let config: AppConfig = toml::from_str(&content)
            .map_err(|e| ConfigError::ParseError(format!("解析配置文件失败: {}", e)))?;
            
        Ok(config)
    }
    
    /// 从环境变量加载配置
    pub fn from_env() -> Result<Self, ConfigError> {
        let cores = num_cpus::get();
        
        // 优先尝试加载配置文件
        if let Ok(config) = Self::from_file("config.toml") {
            return Ok(config);
        }
        
        // 配置文件加载失败，尝试从环境变量加载
        let grpc_endpoint = env::var("GRPC_ENDPOINT").unwrap_or_else(|_| {
            "solana-yellowstone-grpc.publicnode.com:443".to_string()
        });
        
        let redis_url = env::var("REDIS_URL").unwrap_or_else(|_| {
            "redis://127.0.0.1/".to_string()
        });
        
        let redis_channel = env::var("REDIS_CHANNEL").unwrap_or_else(|_| {
            "trades_channel".to_string()
        });

        let bonk_channel = env::var("BONK_CHANNEL").unwrap_or_else(|_| {
            "bonk_channel".to_string()
        });

        let pump_channel = env::var("PUMP_CHANNEL").unwrap_or_else(|_| {
            "pump_channel".to_string()
        });
        
        let log_level = env::var("LOG_LEVEL").unwrap_or_else(|_| {
            "info".to_string()
        });
        
        // 从环境变量构造配置
        let config = Self {
            grpc: GrpcConfig {
                endpoint: grpc_endpoint,
                timeout: env::var("GRPC_TIMEOUT")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(30),
                reconnect_interval: env::var("GRPC_RECONNECT_INTERVAL")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(5),
            },
            redis: RedisConfig {
                url: redis_url,
                channel: redis_channel,
                bonk_channel,
                pump_channel,
                pool_size: env::var("REDIS_POOL_SIZE")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(50),
            },
            processor: ProcessorConfig {
                parser_threads: env::var("PARSER_THREADS")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(cores * 2),
                publisher_threads: env::var("PUBLISHER_THREADS")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(cores),
                grpc_buffer_size: env::var("GRPC_BUFFER_SIZE")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(100000),
                queue_capacity: env::var("QUEUE_CAPACITY")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(1000000),
                batch_size: env::var("BATCH_SIZE")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(1000),
            },
            programs: ProgramsConfig {
                pump_program: env::var("PUMP_PROGRAM_ID").unwrap_or_else(|_| {
                    "54HsPQAHuYZJU8LkJ2Li4AdyVE5hxpNK5SmRnSgg1uns".to_string()
                }),
                bonk_program: env::var("BONK_PROGRAM_ID").unwrap_or_else(|_| {
                    "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj".to_string()
                }),
            },
            logging: LoggingConfig {
                level: log_level,
                file_output: env::var("LOG_FILE_OUTPUT")
                    .ok()
                    .and_then(|v| v.parse().ok())
                    .unwrap_or(false),
                file_path: env::var("LOG_FILE_PATH").unwrap_or_else(|_| "logs/parser.log".to_string()),
            },
        };
        
        Ok(config)
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<(), ConfigError> {
        if self.grpc.endpoint.is_empty() {
            return Err(ConfigError::ValidationError("gRPC 端点不能为空".to_string()));
        }
        
        if self.programs.pump_program.is_empty() {
            return Err(ConfigError::ValidationError("PumpFun 程序 ID 不能为空".to_string()));
        }
        
        Ok(())
    }
    
    /// 创建PipelineConfig
    pub fn to_pipeline_config(&self) -> crate::types::PipelineConfig {
        crate::types::PipelineConfig {
            grpc_buffer_size: self.processor.grpc_buffer_size,
            parser_threads: self.processor.parser_threads,
            publisher_threads: self.processor.publisher_threads,
            queue_capacity: self.processor.queue_capacity,
            batch_size: self.processor.batch_size,
            redis_pool_size: self.redis.pool_size,
            redis_channel: self.redis.channel.clone(),
            bonk_channel: self.redis.bonk_channel.clone(),
            pump_channel: self.redis.pump_channel.clone(),
        }
    }
}

static GLOBAL_CONFIG: OnceLock<AppConfig> = OnceLock::new();

/// 获取全局配置实例
pub fn get_config() -> &'static AppConfig {
    GLOBAL_CONFIG.get_or_init(|| {
        AppConfig::from_env().expect("Failed to load configuration")
    })
}

/// 初始化全局配置
pub fn init_config() -> Result<(), ConfigError> {
    static INIT: Once = Once::new();
    
    INIT.call_once(|| {
        let config = AppConfig::from_env().expect("Failed to load configuration");
        GLOBAL_CONFIG.set(config).expect("Failed to set global configuration");
    });
    
    Ok(())
} 